using GlobalTrader2.Aggregator.UseCases.Account.LoginPreference.LoginPreference.Queries;
using GlobalTrader2.Aggregator.UseCases.Common.GenerateMailTemplate.Queries.ReleaseHUBRFQSendMailTemplate;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.MailMessages;
using GlobalTrader2.Orders.UserCases.Orders.MailGroup.EmailListByGroup.Queries;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries;
using GlobalTrader2.UserAccount.UseCases.MailMessages.Commands.SendNewMessage;

namespace GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseBom
{
    public class NotifyReleaseBomHandler(IMediator mediator, IEmailService emailService, IRazorViewToStringService razorViewToStringService) : IRequestHandler<NotifyReleaseBomCommand, BaseResponse<int>>
    {
        private readonly IMediator _mediator = mediator;
        private readonly IEmailService _emailService = emailService;
        private readonly IRazorViewToStringService _razorViewToStringService = razorViewToStringService;

        public async Task<BaseResponse<int>> Handle(NotifyReleaseBomCommand request, CancellationToken cancellationToken)
        {
            // Process individual logins
            var recipients = request.ToLogins
                .Select(x => new RecipientRequest(
                    Value: x,
                    Type: (int)MailMessageAddressType.Individual
                )).ToList();


            // Process mail groups
            foreach (var groupId in request.ToGroups)
            {
                recipients.Add(new RecipientRequest(
                    Value: groupId,
                    Type: (int)MailMessageAddressType.Group
                ));
            }

            var contentInternal = await _razorViewToStringService.RenderViewToStringAsync("Templates/_NotifyReleaseBom", new Dto.Templates.NotifyReleaseBom()
            {
                HyperLink = request.HUBRFQHyperlink,
                BomName = request.BOMName,
            });

            var sendMessageCommand = new SendNewMessageCommand(request.Subject, contentInternal, recipients, request.SenderLoginNo, request.SenderName)
            {
                CompanyId = request.BomCompanyNo,
                Message = contentInternal,
                Recipients = recipients,
                SenderLoginNo = request.SenderLoginNo,
                SenderName = request.SenderName,
                Subject = request.Subject
            };
            var result = await _mediator.Send(sendMessageCommand, cancellationToken);

            var contentExternal = (await _mediator.Send(new ReleaseHubrfqSendMailTemplateQuery()
            {
                HUBRFQStatus = request.HUBRFQStatus,
                Code = request.Code,
                ClientCurrencyCode = request.ClientCurrencyCode,
                BomId = request.BOMId,
                LoginId = request.LoginId,
                ClientId = request.ClientId,
                IsReleasedAll = request.IsReleasedAll,
                IsPoHub = request.IsPoHub,
                RequirementId = request.RequirementID,
                CultureInfo = request.CultureInfo,
                ClientCurrencyId = request.ClientCurrencyID,
            }, cancellationToken)).Data;

            var loginPreferenceResponse = await _mediator.Send(new LoginPreferenceDetailsCommand { LoginNo = request.SenderLoginNo }, cancellationToken);
            var sendEmail = loginPreferenceResponse.Data?.SendEmail;
            var toEmail = new List<string>();
            var getUserQuery = new GetAllLoginForSendEmailQuery()
            {
                LoginIds = [.. request.ToLogins.Select(x => (int?)x)]
            };

            var getUserResponse = await _mediator.Send(getUserQuery, cancellationToken);
            var email = getUserResponse.Data?.Select(x => x.Email);
            toEmail.AddRange(email ?? []);

            if (request.ToGroups.Length != 0)
            {
                var lstToLoginIDRes = await _mediator.Send(new GetAllEmailListByGroupQuery()
                {
                    MailGroupNos = request.ToGroups
                }, cancellationToken);

                var groupEmails = lstToLoginIDRes.Data!.Select(x => x.EMail!);
                toEmail = [.. toEmail.Concat(groupEmails)
                                 .Where(e => !string.IsNullOrWhiteSpace(e))
                                 .Distinct()];
            }
            if (sendEmail is true && toEmail.Count != 0)
            {
                await _emailService.TrySendEmailAsync(request.SenderEmail!, toEmail, [], [], request.Subject, contentExternal ?? "", [], [], cancellationToken);
            }
            return result;
        }
    }
}
